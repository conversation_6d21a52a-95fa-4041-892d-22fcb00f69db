const maxColorVisible = 6;
const cartWrapper = document.querySelector("#cart")
const cartSidebarWrapper = document.querySelector("#cartSidebar")

const cartUpdater = (function () {
  const renderInitCartOffcanvas = (wrapper) => {
    const cartList = document.querySelector("#cartSidebar .cart__list");
    if (!cartList) return;

    const cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
    if (cartItems.length === 0) return;
    cartList.innerHTML = "";

    cartItems.forEach(item => {
      // Bước 1: Gán lại isActive theo selectedSize / selectedColor
      item.sizes = item.sizes.map(size => ({
        ...size,
        isActive: size.text === item.selectedSize
      }));

      item.colors = item.colors.map(color => ({
        ...color,
        isActive: color.backgroundColor === item.selectedColor
      }));

      // Bước 2: Render HTML size/color
      const sizeHTML = item.sizes.map(size => `
        <li class="product-size__item maybi-hover ${size.isActive ? "product-size--active" : ""}"
            data-color='${size.dataColor || ""}'>${size.text}</li>
      `).join("");

      const colorHTML = item.colors.map(color => `
        <li class="product-color ${color.isActive ? "active" : ""}" data-img="${color.dataImg}">
          <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
        </li>
      `).join("");

      // Bước 3: Render từng item giỏ hàng
      const html = `
        <div class="cart__item product" data-id="${item.product_id}">
          <div class="cart__item__img">
            <img class="product__img" src="${item.image}" alt="${item.name}" />
          </div>
          <div class="cart__item__content product__info">
            <strong class="cart__item__title">
              <a href="#" title="${item.name}">${item.name}</a>
            </strong>
            <div class="cart__item__info">
              <div class="quantity-cart">
                <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
                  <svg class="product-icon__down"><use href="#icon-down"></use></svg>
                </button>
                <input class="quantity-cart__input" type="text" aria-label="Số lượng sản phẩm" value="${item.quantity}" />
                <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
                  <svg class="product-icon__up"><use href="#icon-up"></use></svg>
                </button>
              </div>
              <div class="cart__item__price">
                <span class="cart__item__price--sale">${item.price || "--"}</span>
              </div>
            </div>
            <div class="cart__item__variants">
              <span class="text-muted">Size:</span>
              <ul class="product-size">${sizeHTML}</ul>
            </div>
            <div class="cart__item__variants">
              <span class="text-muted">Màu:</span>
              <ul class="product-colors">${colorHTML}</ul>
            </div>
          </div>
          <div class="cart__item__delete">
            <svg class="cart-icon__delete"><use href="#icon-close"></use></svg>
          </div>
        </div>
      `;
      cartList.insertAdjacentHTML("beforeend", html);
    });

    // Bước 4: Tính tổng tiền
    const subTotal = cartItems.reduce((total, item) => {
      const unitPrice = parseCurrency(item.price || "0");
      return total + unitPrice * item.quantity;
    }, 0);

    var subTotalEl = wrapper.querySelector('[data-type="subtotal"]');
    if (subTotalEl) {
      subTotalEl.textContent = formatCurrency(subTotal);
    }

    // Bước 5: Cập nhật hiển thị liên quan
    updateFreeshipBar(cartSidebarWrapper, subTotal);
    updateProductCount(wrapper);
    updateCartCount(cartItems); // icon header
    cartUpdater.rebind("#cartSidebar"); // bind lại event tăng/giảm, xóa
  };

  const renderInitCart = (wrapper) => {
    const container = document.querySelector(".products-cart");
    if (!container) return;

    const cartContent = document.querySelector(".cart__content");
    const cartSummary = document.querySelector(".cart__summary");
    const cartEmpty = document.querySelector(".cart__empty");

    const cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
    if (cartItems.length === 0){
      
      cartContent?.classList.add("hidden");
      cartSummary?.classList.add("hidden");
      cartEmpty?.classList.remove("hidden");
      return;
    };

    cartContent?.classList.remove("hidden");
    cartSummary?.classList.remove("hidden");
    cartEmpty?.classList.add("hidden");

    //render demo -----------
    wrapper.querySelector('[data-type="shipping"]').textContent = "30.000đ";
    wrapper.querySelector('[data-type="discount"]').textContent = "-30.000đ";
    const selectedVouchersEl = wrapper.querySelector('.cart-vouchers__selected');
    selectedVouchersEl.style.display = '';
    const cartSummarySaveEl = wrapper.querySelector('.cart-summary__save');
    cartSummarySaveEl.style.display = '';
    

    // Xoá các sản phẩm cũ nếu có (giữ lại cart-header)
    const existingItems = container.querySelectorAll(".cart__item.product__item.product");
    existingItems.forEach(el => el.remove());

    [...cartItems].reverse().forEach(item => {
      const unitPrice = parseCurrency(item.price || "0");
      const subtotal = formatCurrency(unitPrice * item.quantity);

      const sizeHTML = item.sizes.map(size => `
      <li class="product-size__item maybi-hover ${size.isActive ? "product-size--active" : ""}"
          data-color='${size.dataColor}'>${size.text}</li>
    `).join("");

      const colorHTML = item.colors.map(color => `
      <li class="product-color ${color.isActive ? "active" : ""}" data-img="${color.dataImg}">
        <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
      </li>
    `).join("");

      const html = `
      <div class="cart__item product__item product">
        <div class="col__information">
          <div class="col__information__img">
            <img class="product__img" src="${item.image}" alt="${item.name}" />
          </div>
          <div class="col__information__content product__info">
            <strong class="cart__item__title">
              <a href="#" title="${item.name}">${item.name}</a>
            </strong>
            <div class="cart__item__variants">
              <span class="text-muted">Size:</span>
              <ul class="product-size">${sizeHTML}</ul>
            </div>
            <div class="cart__item__variants">
              <span class="text-muted">Màu:</span>
              <ul class="product-colors">${colorHTML}</ul>
            </div>
          </div>
        </div>
        <strong class="col__price text-muted cart__item__price--sale text-center" data-label="Price">${item.price}</strong>
        <div class="quantity-cart">
          <button class="quantity-cart__btn quantity-cart__btn-down" type="button" aria-label="Giảm số lượng">
            <svg class="product-icon__down"><use href="#icon-down"></use></svg>
          </button>
          <input class="quantity-cart__input" type="text" aria-label="Số lượng sản phẩm" value="${item.quantity}" />
          <button class="quantity-cart__btn quantity-cart__btn-up" type="button" aria-label="Tăng số lượng">
            <svg class="product-icon__up"><use href="#icon-up"></use></svg>
          </button>
        </div>
        <div class="col__total product-total text-center" data-label="Subtotal">${subtotal}</div>
        <div class="col__delete cart__item__delete">
          <svg class="col-icon__delete"><use href="#icon-close"></use></svg>
        </div>
      </div>
    `;

      // ➤ Thêm sau .cart-header
      const cartHeader = container.querySelector(".cart-header");
      cartHeader.insertAdjacentHTML("afterend", html);
    });

    // Tính tổng tiền
    const totalPrice = cartItems.reduce((total, item) => {
      const unitPrice = parseCurrency(item.price || "0");
      return total + unitPrice * item.quantity;
    }, 0);

    var subTotalEl = wrapper.querySelector('[data-type="subtotal"]');
    if (subTotalEl) {
      subTotalEl.textContent = formatCurrency(totalPrice);
    }
    updateFreeshipBar(wrapper, totalPrice);
    renderTotalPrice(wrapper, totalPrice);
    cartUpdater.rebind("#cart")
  };

  function addToCartSmart(productData) {
    const cartList = document.querySelector("#cartSidebar .cart__list");
    if (!cartList) return;

    let cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");

    // Lấy size và color đang được chọn
    const selectedSize = productData.sizes.find(size => size.isActive)?.text || "";
    const selectedColor = productData.colors.find(color => color.isActive)?.backgroundColor || "";

    // Gắn thêm vào dữ liệu để so sánh và lưu lại
    productData.selectedSize = selectedSize;
    productData.selectedColor = selectedColor;

    // Kiểm tra trùng sản phẩm
    const matchedIndex = cartItems.findIndex(item =>
      item.product_id === productData.product_id &&
      item.selectedSize === selectedSize &&
      item.selectedColor === selectedColor
    );

    if (matchedIndex !== -1) {
      // Nếu trùng thì tăng số lượng
      cartItems[matchedIndex].quantity += productData.quantity;
    } else {
      // Nếu mới thì thêm vào
      cartItems.push(productData);
    }

    // Lưu lại localStorage
    localStorage.setItem("cartItems", JSON.stringify(cartItems));

    // Re-render lại toàn bộ cart (đảm bảo đồng bộ DOM & storage)
    renderInitCartOffcanvas(cartSidebarWrapper);
  }

  const bindQuantityButtons = (wrapper) => {
    const minusBtns = wrapper.querySelectorAll(".quantity-cart__btn-down");
    const plusBtns = wrapper.querySelectorAll(".quantity-cart__btn-up");
    const quantityInputs = wrapper.querySelectorAll(".quantity-cart__input");

    minusBtns.forEach((btn) =>
      btn.addEventListener("click", () => changeQuantity(wrapper, btn, -1))
    );

    plusBtns.forEach((btn) =>
      btn.addEventListener("click", () => changeQuantity(wrapper, btn, 1))
    );

    quantityInputs.forEach((input) => {
      input.addEventListener("input", () => validateAndUpdateInput(wrapper, input));
      input.addEventListener("change", () => validateAndUpdateInput(wrapper, input));
    });
  };

  const bindDeleteButtons = (wrapper) => {
    const deleteBtns = wrapper.querySelectorAll(".cart__item__delete");
    deleteBtns.forEach((btn) => {
      btn.addEventListener("click", (e) => {
        e.stopPropagation();
        const item = btn.closest(".cart__item");
        if (item) {
          item.remove();
          updateCartSummary(wrapper);
          syncCartToLocalStorage(wrapper);
        }
      });
    });
  };

  const updateCartSummary = (wrapper) => {
    const items = wrapper.querySelectorAll(".cart__item");
    let total = 0;
    const cartItems = [];

    items.forEach((item) => {
      const priceText = item.querySelector(".cart__item__price--sale")?.textContent || "0";
      const input = item.querySelector(".quantity-cart__input") || item.querySelector(".cart__item__quantity--number span");
      const quantity = parseInt(input?.value || input?.textContent || "1", 10);
      const price = parseCurrency(priceText);
      total += quantity * price;

      cartItems.push({
        quantity
      });
    });

    renderSubTotalPrice(wrapper, total);
    updateFreeshipBar(wrapper, total);
    updateProductCount(wrapper);
    updateCartCount(cartItems);
    renderTotalPrice(wrapper, total);

    const freeshipBox = wrapper.querySelector(".freeship_progress");
    if (freeshipBox) freeshipBox.setAttribute("data-current", total);
  };

  const changeQuantity = (wrapper, btn, delta) => {
    const input = btn.closest(".quantity-cart")?.querySelector(".quantity-cart__input");
    if (!input) return;

    let quantity = parseInt(input.value || "1", 10);
    quantity = Math.max(1, quantity + delta);
    input.value = quantity;

    // update total of this product (optional)
    const cartItem = btn.closest(".cart__item");
    const priceEl = cartItem.querySelector(".cart__item__price--sale");
    const totalEl = cartItem.querySelector(".product-total");

    if (priceEl && totalEl) {
      const unitPrice = parseCurrency(priceEl.textContent || "0");
      const total = quantity * unitPrice;
      totalEl.textContent = formatCurrency(total);
    }


    updateCartSummary(wrapper);
    syncCartToLocalStorage(wrapper);
  };

  const validateAndUpdateInput = (wrapper, input) => {
    let value = parseInt(input.value || "1", 10);
    if (isNaN(value) || value < 1) value = 1;
    input.value = value;

    // update total of this product (optional)
    const cartItem = input.closest(".cart__item");
    const priceEl = cartItem.querySelector(".cart__item__price--sale");
    const totalEl = cartItem.querySelector(".product-total");

    if (priceEl && totalEl) {
      const unitPrice = parseCurrency(priceEl.textContent || "0");
      const total = value * unitPrice;
      totalEl.textContent = formatCurrency(total);
    }


    updateCartSummary(wrapper);
    syncCartToLocalStorage(wrapper);
  };

  const renderSubTotalPrice = (wrapper, total) => {
    var subTotalEl = wrapper.querySelector('[data-type="subtotal"]');
    if (total === 0) {
      subTotalEl.textContent = 0;
      return;
    }
    if (subTotalEl) {
      subTotalEl.textContent = formatCurrency(total);
    }
  };

  const renderTotalPrice = (wrapper, total) => { 
    var shippingEl = wrapper.querySelector('[data-type="shipping"]');
    var discountEl = wrapper.querySelector('[data-type="discount"]');
    var totalPriceEl = wrapper.querySelector('[data-type="total"]');

    if (!totalPriceEl) return;

    if (total === 0){
      shippingEl.textContent = 0;
      discountEl.textContent = 0;
      totalPriceEl.textContent = 0;
      return;
    }

    let shippingFee = 0;
    let discountAmount = 0;

    if (shippingEl) {
      const shippingText = shippingEl.textContent.trim();
      if (shippingText !== "Miễn phí") {
        shippingFee = parseInt(shippingText.replace(/[^\d]/g, ''), 10) || 0;
      }
    }

    if (discountEl) {
      const discountText = discountEl.textContent.trim();
      const numeric = parseInt(discountText.replace(/[^\d]/g, ''), 10);

      if (!isNaN(numeric) && numeric > 0) {
        discountAmount = numeric;
      }
    }

    const finalTotal = total - shippingFee - discountAmount;
    totalPriceEl.textContent = formatCurrency(finalTotal);
  };

  const updateProductCount = (wrapper) => {
    const countEl = wrapper.querySelector(".cart__total__count");
    const items = wrapper.querySelectorAll(".cart__item");
    let totalQuantity = 0;

    items.forEach((item) => {
      const input = item.querySelector(".quantity-cart__input") || item.querySelector(".cart__item__quantity--number span");
      const quantity = parseInt(input?.value || input?.textContent || "1", 10);
      totalQuantity += quantity;
    });

    if (countEl) countEl.textContent = totalQuantity;
  };

  const triggerCartOffcanvas = () => {
    document.addEventListener("click", function (e) {
      const btn = e.target.closest(".btn_add_to_cart");
      if (!btn) return;

      const productEl = btn.closest(".product");
      if (!productEl) return;

      // 1. Thông tin chính
      const name = productEl.querySelector(".product__title > a")?.textContent.trim() || "";
      const productId = btn.dataset.productId || "";
      const image = productEl.querySelector(".product__img")?.getAttribute("src") || "";
      const price = productEl.querySelector(".sale-price")?.textContent.trim() || "";

      // 2. Danh sách size (có thể chứa `data-color`)
      const sizeElements = productEl.querySelectorAll(".product-size__item");
      const sizes = Array.from(sizeElements).map((el) => ({
        text: el.textContent.trim(),
        isActive: el.classList.contains("product-size--active"),
        dataColor: el.getAttribute("data-color") || null,
      }));

      // 3. Danh sách màu
      // 1. Lấy size đang active
      const activeSizeEl = productEl.querySelector(".product-size__item.product-size--active");
      let colors = [];

      if (activeSizeEl) {
        const rawDataColor = activeSizeEl.getAttribute("data-color");

        try {
          const parsed = JSON.parse(rawDataColor || "[]");

          // 3. Lấy màu đang active từ HTML (dù HTML rút gọn)
          const activeColorEl = productEl.querySelector(".product-color.active");
          const activeColorImg = activeColorEl?.getAttribute("data-img");

          // 4. Parse danh sách màu và đánh dấu isActive
          colors = parsed.map((item) => ({
            backgroundColor: item.color,
            dataImg: item.img,
            isActive: item.img === activeColorImg,
          }));
        } catch (e) {
          console.error("Không parse được data-color:", e);
        }
      }


      // 4. Số lượng
      const quantityInput = productEl.querySelector(".quantity-input");
      const quantity = quantityInput ? parseInt(quantityInput.value || "1", 10) : 1;

      const productData = {
        product_id: productId,
        name,
        image,
        sizes,
        colors,
        quantity,
        price
      };

      addToCartSmart(productData);
      updateCartSummary(cartSidebarWrapper);

      cartSidebarWrapper?.classList.add("is-open");
    });
  };

  return {
    init(selector) {
      const wrapper = document.querySelector(selector);
      if (!wrapper) return false;
      renderInitCartOffcanvas(cartSidebarWrapper);
      renderInitCart(cartWrapper);
      changePruductColor(wrapper);
      changePruductSize(wrapper);
      bindDeleteButtons(wrapper);
      triggerCartOffcanvas();
      return true;
    },
    rebind(selector) {
      const wrapper = document.querySelector(selector);
      if (!wrapper) return;
      bindQuantityButtons(wrapper);
      bindDeleteButtons(wrapper);
      changePruductSize(wrapper);
    }
  };
})();

document.addEventListener("DOMContentLoaded", () => {
  cartUpdater.init("#cartSidebar");
  cartUpdater.init("#cart");
});


function formatCurrency(number) {
  return number.toLocaleString("vi-VN") + "đ";
}

function parseCurrency(text) {
  return parseInt(text.replace(/[^\d]/g, ""), 10) || 0;
}

const changePruductColor = (wrapper) => {
  const containers = ["#cartSidebar", "#cart"];

  containers.forEach((selector) => {
    const wrapper = document.querySelector(selector);
    if (!wrapper) return;
    wrapper.addEventListener("click", (e) => {
      const color = e.target.closest(".product-color");
      if (!color || color.classList.contains("product-color--more")) return;

      const img = color.getAttribute("data-img");
      const product = color.closest(".product");
      const mainImg = product.querySelector(".product__img");

      // Xóa trạng thái active cũ
      const allColors = product.querySelectorAll(".product-color");
      allColors.forEach((c) => c.classList.remove("active"));
      color.classList.add("active");

      // Đổi ảnh chính nếu có
      if (mainImg && img) {
        mainImg.setAttribute("src", img);
      }

      syncCartToLocalStorage(wrapper);
    });
  });
};



const changePruductSize = (wrapper) => {
  const sizes = wrapper.querySelectorAll(".product-size__item");
  sizes.forEach((item) => {
    item.addEventListener("click", (el) => {
      if (!el.target.dataset.color) return;
      const colorItems = JSON.parse(el.target.dataset.color);
      const productWrapper = el.currentTarget.closest(".product");
      const productInfo = productWrapper.querySelector(".product__info");
      const productColors = productInfo.querySelector(".product-colors");

      const currentActiveColor = productColors.querySelector(
        ".product-color.active"
      );
      const currentImg = currentActiveColor?.getAttribute("data-img");

      let colorHTML = "";

      colorItems.forEach((colorItem) => {
        colorHTML += `
              <li class="product-color" data-img="${colorItem.img}">
                <span class="product-color__item" style="background-color: ${colorItem.color};"></span>
              </li>`;
      });



      productColors.innerHTML = colorHTML;

      const newColorElements =
        productColors.querySelectorAll(".product-color");
      let foundMatch = false;

      newColorElements.forEach((colorEl) => {
        if (colorEl.getAttribute("data-img") === currentImg) {
          colorEl.classList.add("active");
          foundMatch = true;
        }
      });

      if (!foundMatch && newColorElements.length > 0) {
        newColorElements[0].classList.add("active");
      }

      const activeColor = productColors.querySelector(
        ".product-color.active"
      );
      const newImg = activeColor?.getAttribute("data-img");
      const mainImg = productWrapper.querySelector(".product__img");
      if (mainImg && newImg) {
        mainImg.setAttribute("src", newImg);
      }

      const activeSize = productWrapper.querySelector(
        ".product-size--active"
      );
      if (activeSize) {
        activeSize.classList.remove("product-size--active");
      }
      el.currentTarget.classList.add("product-size--active");

      changePruductColor(wrapper);
      syncCartToLocalStorage(wrapper);
    });
  });
};





/**
 * Cập nhật giỏ hàng vào localStorage dựa trên DOM hiện tại của off‑canvas cart
 */
const syncCartToLocalStorage = (wrapper) => {
  const updatedCart = [];

  wrapper.querySelectorAll(".cart__item").forEach((itemEl) => {
    // 1) Thông tin cơ bản
    const productId = itemEl.getAttribute("data-id") || "";                    // cần gán sẵn trong HTML
    const name      = itemEl.querySelector(".cart__item__title > a")?.textContent.trim() || "";
    const image     = itemEl.querySelector(".product__img")?.getAttribute("src") || "";
    const price     = itemEl.querySelector(".cart__item__price--sale")?.textContent.trim() || "";
    const quantity  = parseInt(itemEl.querySelector(".quantity-cart__input")?.value || "1", 10);

    // 2) Lấy size & color đang active (QUAN TRỌNG để nhận diện sản phẩm)
    const selectedSize   = itemEl.querySelector(".product-size--active")?.textContent.trim() || "";
    const selectedColorSpan = itemEl.querySelector(".product-color.active .product-color__item");
    const currentStyle = selectedColorSpan?.getAttribute("style") || "";
    const selectedColor = currentStyle
      .split("background-color:")[1]
      ?.split(";")[0]
      ?.trim() || "";


    // 3) Danh sách size/color đầy đủ (để render lại về sau)
    const sizes  = Array.from(itemEl.querySelectorAll(".product-size__item")).map((el) => ({
      text      : el.textContent.trim(),
      isActive  : el.classList.contains("product-size--active"),
      dataColor : el.getAttribute("data-color") || null,
    }));

    const colors = Array.from(itemEl.querySelectorAll(".product-color")).map((el) => ({
      backgroundColor : el.querySelector(".product-color__item").getAttribute("style").split("background-color:")[1]
      ?.split(";")[0]
      ?.trim() || "",
      dataImg         : el.getAttribute("data-img") || "",
      isActive        : el.classList.contains("active"),
    }));

    // 4) Push vào mảng cart
    updatedCart.push({
      product_id   : productId,
      name,
      image,
      price,
      quantity,
      selectedSize,
      selectedColor,
      sizes,
      colors,
    });
  });

  // 5) Lưu lại localStorage
  localStorage.setItem("cartItems", JSON.stringify(updatedCart));
};


function updateCartCount(cartItems) {
  const countEl = document.querySelector(".cart-btn__count");
  if (!countEl) return;

  const totalQuantity = cartItems.reduce((sum, item) => sum + (parseInt(item.quantity, 10) || 0), 0);

  if (totalQuantity > 0) {
    countEl.textContent = totalQuantity;
    countEl.style.visibility = "visible";
    countEl.style.opacity = "1";
  } else {
    countEl.style.visibility = "hidden";
    countEl.style.opacity = "0";
  }
}

function updateFreeshipBar(wrapper, current) {
  const freeshipBox = wrapper.querySelector(".freeship_progress");
  if (!freeshipBox) return;

  const target = parseInt(freeshipBox.dataset.target || "0", 10);
  const needMsg = freeshipBox.querySelector(".freeship-message-need");
  const successMsg = freeshipBox.querySelector(".freeship-message-success");
  const valueBar = freeshipBox.querySelector(".freeship_progress__bar-value");
  const amountText = freeshipBox.querySelector(".freeship-icon__amount");
  const endText = freeshipBox.querySelector(".freeship_progress__bar-end");
  var shippingEl = document.querySelector('[data-type="shipping"]');

  if (valueBar){
    const progress = Math.min((current / target) * 100, 100);
    valueBar.style.width = `${progress}%`;
  } 

  if (amountText) {
    amountText.textContent = formatCurrency(current);
  }

  if (endText) {
    endText.textContent = formatCurrency(target);
  }

  const isFreeShipping = current >= target;
  // Ẩn/hiện message
  toggleVisibility(successMsg, isFreeShipping, "inline-block");
  toggleVisibility(needMsg, !isFreeShipping);

  // Cập nhật giá trị cần đạt
  if (!isFreeShipping && needMsg) {
    const remaining = target - current;
    const bold = needMsg.querySelector("b");
    if (bold) bold.textContent = formatCurrency(remaining);
  }
  
  // Cập nhật phí vận chuyển
  if (shippingEl) {
    shippingEl.textContent = isFreeShipping ? "Miễn phí" : "30.000đ";
  }
}

function toggleVisibility(el, show, display = "block") {
  if (!el) return;
  el.classList.toggle("hidden", !show);
  el.style.display = show ? display : "none";
}



