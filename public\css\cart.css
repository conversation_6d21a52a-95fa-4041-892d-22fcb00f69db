.cart{
    font-size: var(--10px);
}

.cart__wrapper {
    gap: 3em;
    display: flex;
    padding: 8em 0 6em;
    justify-content: center;
    min-height: 22.5em;
}

.cart__content {
    width: 70%;
}

.cart__notification-sold {
  margin-bottom: 2em;
  padding: 0.5em 1em;
  border-radius: 0.8em;
  display: flex;
  align-items: center;
  gap: 1.2em;
  background-color: rgb(247, 247, 247);
  font-size: 1.6em;
}
.cart__notification-sold > .icon-fire {
  width: 2.4em;
  height: 2.4em;    
  animation: tf-ani-flash 500ms infinite; 
}

@keyframes tf-ani-flash {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.notification-sold__count{
    line-height: 1.6;
}

.notification-sold__time {
    display: inline-block;
    min-width: 5ch;
    text-align: center;
    color: red;
    font-weight: bold;
}

.products-cart {
  display: grid;
  gap: 1em;
  margin: auto;
  padding: 1em;
  box-sizing: border-box;
}

.cart-header {
  display: grid;
  grid-template-columns: 3fr 1fr 2fr 1fr 1fr;
  font-weight: bold;
  padding-bottom: 2em;
  border-bottom: 0.1em solid rgba(0, 0, 0, 0.4);  
  column-gap: 2em;
  align-items: center;
}

.cart-header__title{
    font-size: 1.6em;
}

.product__item{
    position: relative;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 2em;
    border-bottom: 0.1em solid rgba(0, 0, 0, 0.2);
    justify-content: space-between;
    gap: 2em;
    display: grid; 
    grid-template-columns: 3fr 1fr 2fr 1fr 1fr;
}
.col__information {
    display: flex;
    align-items: center;
    gap: 1em;
    width: 100%;
    min-width: 0;
}
.col__information__img{
    font-size: var(--10px);
    border-radius: 0.8em;
    overflow: hidden;
    aspect-ratio: 3 / 4;
    width: 10.1em;
}   
.col__information__img > img{
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    object-position: center;
    transition-duration: 700ms;
}
.col__information__content{
    flex: 1;
    min-width: 0;
    gap: 1em;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: transparent;
    box-shadow: none;
}

.cart__item__title{
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: all 0.3s ease;
} 

.cart__item__title:hover{
    color: var(--primary-hover)
} 

.cart__item__variants{
    display: flex;
    gap: 1em;
    align-items: center;
}


.cart__item__variants > .product-size{
    opacity: unset;
    flex-direction: row;
    width: fit-content;
    position: unset;
    background-color: transparent;
}

.cart__item__variants > span{
    font-size: 1.4em;
    width: fit-content;
}

.product-colors {
    overflow-x: auto;
    -ms-overflow-style: none;     /* IE & Edge */
    scrollbar-width: none;        /* Firefox */
}

.product-colors::-webkit-scrollbar {
    display: none;                /* Chrome, Safari */
}

.product-color {
    flex: 0 0 auto;
}

.cart__item__content.product__info{
    padding: 0;
    background-color: transparent;
    box-shadow: none;
}

.cart__total__item{
    font-size: 1.6em;
    font-weight: 600;
}
.cart__action{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}

.col__price{
    font-size: 1.6em;
}

.quantity-cart{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}

.quantity-cart__btn{
    border: .1em solid var(--black);
    background-color: var(--black);
    color: var(--white);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-cart__btn >.product-icon__down, 
.quantity-cart__btn >.product-icon__up{
  font-size: var(--10px);
  width: 2.7em;
  height: 2.7em;
  fill: var(--white);
}

.quantity-cart__input{
  padding: 0.5em;
  font-size: var(--10px);
  text-align: center;
  border: .1em solid var(--black);
  width: 2.8em;
  height: 2.8em;
  border-radius: 50%;
}

.col__total{
    font-weight: 500;
    font-size: 1.6em;
}

.col__delete{
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--10px);
    background-color: var(--black);
    border-radius: 50%;
    width: 4.2em;
    height: 4.2em;
    padding: 1em;
}

.cart__action__item{
    width: 50%;
}

.col-icon__delete{
    width: 100%;
    height: 100%;
    fill: var(--white);
}

.cart__summary {
    font-size: var(--10px);
    width: 30%;
}

.cart__summary__title{
    font-size: 2em;
    height: fit-content;
    margin-bottom: 0.5em;
}

.cart-summary__box{
    border-radius: 3em;
    border: 0.1em solid var(--secondary);
    padding: 3.5em 3em;
}

.cart-vouchers__button, .cart-vouchers__show {
    border: 0.1em solid var(--secondary);
    border-radius: 0.625em;
    width: 100%;
    padding: 0.75em 1.875em;
    background-color: transparent;
    font-weight: 500;
    font-size: 1.6em;
    color: var(--black);
    margin-top: 1.2em;
}

.cart-vouchers__show{
    display: flex;
    align-items: center;
    gap: 0.625em;
}

.cart-vouchers__icon{
    width: 3.125em;
    height: 3.125em;
    opacity: 0.6;
}

.cart-vouchers__selected{
    margin-top: 1.8em;
}

.cart-summary__save{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    margin-top: 1.8em;
}

.cart-save__icon{
    width: 3.2em;
    height: 3.2em;
    margin-right: 1em;
    fill: green;
}

.cart-summary__save > span{
    font-size: 1.6em;
}

.cart-summary__total{
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2em 0 2em;
    gap: 2em;
    border-top: 0.1em solid rgba(0, 0, 0, 0.2);
    margin-top: 2em;
}

.cart-summary__total-row{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cart-summary__total-row > span{
    font-size: 1.4em;
}

.cart-summary__total-row > strong{
    font-size: 1.4em;
}

.cart-summary__total-row:last-child{
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary);
}
.cart-summary__total-row:last-child{
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary);
}




.cart-order__button{
    font-size: 1.6em;
    width: 100%;
    padding: 1em 3em;
    font-weight: 500;
    line-height: 1.2;
    border-radius: 1em;
}

.cart__empty{
    font-size: var(--10px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1em;
}

.cart__empty > img {
    width: 35em;
    height: 35em;
}

.cart__empty > h3 {
    font-size: 2.2em;
}

.cart__empty > p, .cart__empty > a {
    font-size: 1.6em;
}

.cart__empty > a {
    margin-top: 0.5em;
}

/* -------- Responsive -------- */
@media (max-width: 575px){   
    .cart__empty > a {
        font-size: 2em;
        margin-top: 0;
    }
}
@media (min-width: 576px) and (max-width: 768px){

}

@media (max-width: 768px){  
    .page-banner{
        height: 14em;
    }

    .page-banner__title{
        font-size: 1.8em;
    }
    .page-banner__breadcrumb-list{
        font-size: 1.3em;
    }
    .cart__wrapper{
        flex-direction: column;
        padding-top: 4em;
    }

    .cart__content, .cart__summary{
        width: 100%;
    }

    .notification-sold__count{
        font-size: 0.8em;
    }

    .cart-header__title{
        font-size: 1.2em;
    }
    .voucher__section-title{
        font-size: 2.6vw;
    }

    .cart-vouchers__button, .cart-vouchers__show {
        padding: 1.3em 1.875em;
    }

    .cart-vouchers__show{
        justify-content: center;
    }
}

@media only screen and (min-width: 769px) and (max-width: 1024px) {
  
}

@media only screen and (min-width: 992px) and (max-width: 1199px){
   
    
}

@media only screen and (min-width: 1200px) and (max-width: 1399px){
    
}