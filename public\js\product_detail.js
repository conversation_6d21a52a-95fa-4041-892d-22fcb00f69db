const product_detail = (function () {
  const selectedFiles = [];
  const selectedFileKeys = new Set();
  const maxTotal = 6;
  const previewContainer = document.getElementById("file_preview");
  let idCounter = 0;

  const swiperProductDetail = new Swiper(".swiper-product-detail-img-main", {
    slidesPerView: 1,
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    speed: 0,
    preloadImages: true,
    lazy: false,
  });
  new Swiper(".related-product__swiper", {
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: true,
    },
    navigation: {
      nextEl: ".related-product__next",
      prevEl: ".related-product__prev",
    },
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    breakpoints: {
      0: {
        slidesPerView: 2,
      },
      769: {
        slidesPerView: 5,
      },
    },
  });
  function calcSpaceBetweenFromVW(em) {
    const fs = (window.innerWidth * 0.652) / 100;
    if (fs > 20) return 20;
    return em * fs;
  }
  document
    .querySelectorAll(".product-thumbnails >img")
    .forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        swiperProductDetail.slideTo(index);
        // Toggle active class
        document
          .querySelectorAll(".product-thumbnails >img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });

  // Optional: set active on initial load
  document.querySelectorAll(".comma-separated").forEach((container) => {
    const paragraphs = container.querySelectorAll("p");

    paragraphs.forEach((p) => {
      const items = p.querySelectorAll(".comma-item");
      items.forEach((item, index) => {
        const next = item.nextSibling;
        if (
          next &&
          next.nodeType === Node.TEXT_NODE &&
          next.textContent.trim() === ","
        ) {
          next.remove();
        }
        if (index < items.length - 1) {
          item.insertAdjacentText("afterend", ", ");
        }
      });
    });
  });

  const initOffcanvasSupport = () => {
    const offcanvas = document.getElementById("product-offcanvas");
    if (!offcanvas) return;

    const content = document.getElementById("offcanvas-content");
    const overlay = offcanvas.querySelector(".offcanvas__overlay");
    const closeBtn = offcanvas.querySelector(".offcanvas__close");

    const showOffcanvasFromTemplate = (templateId) => {
        const template = document.getElementById(templateId);
        if (!template) return;

        content.innerHTML = template.innerHTML;
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        document.body.style.paddingRight = `${scrollbarWidth}px`;
        offcanvas.classList.add("active");
        document.body.classList.add("offcanvas-open");
    };

    const hideOffcanvas = () => {
        offcanvas.classList.remove("active");
        content.innerHTML = "";
        document.body.style.paddingRight = '';
        document.body.classList.remove("offcanvas-open");
    };

    document.querySelector(".product-detail__return-policy")?.addEventListener("click", () => {
        showOffcanvasFromTemplate("offcanvas-content-return");
    });

    document.querySelector(".product-detail__size-guide")?.addEventListener("click", () => {
        showOffcanvasFromTemplate("offcanvas-content-size");
    });

    overlay?.addEventListener("click", hideOffcanvas);
    closeBtn?.addEventListener("click", hideOffcanvas);
  };

  const changePruductColor = () => {
    document.querySelectorAll(".product").forEach((product) => {
      product.addEventListener("click", (e) => {
        const color = e.target.closest(".product-color");
        if (!color || color.classList.contains("product-color--more")) return;

        const img = color.getAttribute("data-img");
        const productWrapper = color.closest(".product");
        const activeSlide = productWrapper.querySelector(
          ".swiper-slide-active"
        );
        let mainImg = "";
        if (activeSlide) {
          mainImg = activeSlide.querySelector(".product__img");
        } else {
          mainImg = productWrapper.querySelector(".product__img");
        }
        const allColors = [
          ...productWrapper.querySelectorAll(".product-color"),
        ].filter((el) => el.closest(".product") === productWrapper);
        allColors.forEach((c) => c.classList.remove("active"));
        color.classList.add("active");
        if (mainImg && img) {
          mainImg.setAttribute("src", img);
        }
      });
    });
  };

  //process upload img/video
  function bytesToMB(bytes) {
    return bytes / (1024 * 1024);
  }
  function createFileId() {
    return `file-${idCounter++}`;
  }
  function addFiles(newFiles) {
    const imageCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    ).length;
    const videoCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    ).length;

    let newImageCount = 0;
    let newVideoCount = 0;
    let hasNewFile = false;
    let duplicateFiles = [];
    let errorMessages = [];
    const validFiles = [];

    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const fileKey = file.name + "_" + file.lastModified;

      if (selectedFileKeys.has(fileKey)) {
        duplicateFiles.push(file.name);
        continue;
      }

      const isImage = file.type.startsWith("image/");
      const isVideo = file.type.startsWith("video/");

      if (isVideo) {
        if (bytesToMB(file.size) > 60) {
          errorMessages.push(`Video "${file.name}" vượt quá giới hạn 60MB.`);
          continue;
        }
        if (videoCount + newVideoCount >= 1) {
          errorMessages.push(`Đã đủ 1 video. "${file.name}" bị bỏ qua.`);
          continue;
        }
        newVideoCount++;
      }

      if (isImage) {
        if (imageCount + newImageCount >= 5) {
          errorMessages.push("Chỉ được phép tải tối đa 5 ảnh.");
          break;
        }
        newImageCount++;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      if (errorMessages.length > 0) {
        alert(errorMessages.join("\n"));
      }
      return;
    }

    // Kiểm tra tổng giới hạn file
    const totalCount = selectedFiles.length + validFiles.length;
    if (totalCount > 6) {
      errorMessages.push("Tổng cộng chỉ được phép tải tối đa 6 tệp (5 ảnh + 1 video).");
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      alert(errorMessages.join("\n"));
      return;
    }

    // Thêm vào danh sách
    for (const file of validFiles) {
      const fileKey = file.name + "_" + file.lastModified;
      const id = createFileId();
      selectedFiles.push({ file, id, fileKey });
      selectedFileKeys.add(fileKey);
      hasNewFile = true;
    }

    if (duplicateFiles.length > 0) {
      errorMessages.push(
        `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
      );
    }

    if (errorMessages.length > 0) {
      alert(errorMessages.join("\n"));
    }

    if (hasNewFile) {
      renderAllPreviews();
    }
  }

  function renderAllPreviews() {
    previewContainer.innerHTML = "";
    const images = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    );
    const videos = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    );

    [...images, ...videos].forEach(({ file, id }) => {
      renderSinglePreview(
        file,
        id,
        file.type.startsWith("image/") ? "image" : "video"
      );
    });
  }
  function renderSinglePreview(file, id, type) {
    const reader = new FileReader();

    reader.onload = function (e) {
      let el;
      if (type === "image") {
        el = document.createElement("img");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
      } else if (type === "video") {
        el = document.createElement("video");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
        el.controls = true;
      }
      const wrapper = document.createElement("div");
      wrapper.classList.add("preview-wrapper");
      wrapper.setAttribute("data-id", id);
      const removeBtn = document.createElement("button");
      removeBtn.innerHTML =
        '<svg class="cart-icon__delete"><use href="#icon-close"></use></svg>';
      removeBtn.classList.add("preview-remove");
      removeBtn.addEventListener("click", () => {
        const index = selectedFiles.findIndex((f) => f.id === id);
        if (index !== -1) {
          const fileKey = selectedFiles[index].fileKey;
          selectedFiles.splice(index, 1);
          selectedFileKeys.delete(fileKey);
        }
        wrapper.remove();
      });
      wrapper.appendChild(el);
      wrapper.appendChild(removeBtn);
      previewContainer.appendChild(wrapper);
    };
    reader.readAsDataURL(file);
  }
  function handleImageInput(e) {
    addFiles(e.target.files, "image");
    e.target.value = "";
  }
  function handleVideoInput(e) {
    addFiles(e.target.files, "video");
    e.target.value = "";
  }
  function initCommentFileUploadPreview() {
    const imageInput = document.getElementById("upload_image");
    const videoInput = document.getElementById("upload_video");

    if (imageInput) {
      imageInput.addEventListener("change", handleImageInput);
    }

    if (videoInput) {
      videoInput.addEventListener("change", handleVideoInput);
    }
  }
  initCommentFileUploadPreview();

  function syncHeights() {
    const left = document.querySelector(".product-detail__content-left");
    const right = document.querySelector(".product-detail__content-right");
    if (!left || !right) return;
    if (document.documentElement.clientWidth <= 768) {
      left.style.minHeight = "0";
      return;
    }
    left.style.minHeight = "0";
    const rightHeight = right.getBoundingClientRect().height;
    const rightWidth = right.getBoundingClientRect().width;
    const paddingBottom = rightWidth * 0.05;

    const reducedHeight = Math.ceil(rightHeight - paddingBottom);
    left.style.minHeight = `${reducedHeight}px`;
  }

  function initStickyHeightSync() {
    syncHeights();
    window.addEventListener("resize", syncHeights);
    const rightEl = document.querySelector(".product-detail__content-right");
    if (rightEl) {
      const observer = new MutationObserver(syncHeights);
      observer.observe(rightEl, { childList: true, subtree: true });
    }
  }

  const initTabSwitcher = () => {
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");

    tabButtons.forEach((btn, index) => {
      btn.addEventListener("click", () => {
        tabButtons.forEach((b) => {
          b.classList.remove("active");
          b.setAttribute("aria-selected", "false");
          b.setAttribute("tabindex", "-1");
        });

        tabPanes.forEach((pane) => {
          pane.classList.remove("active");
          pane.setAttribute("hidden", "true");
        });

        btn.classList.add("active");
        btn.setAttribute("aria-selected", "true");
        btn.setAttribute("tabindex", "0");

        const target = index === 0 ? "description" : "reviews";
        const targetPane = document.querySelector(
          `.tab-pane[data-tab="${target}"]`
        );

        if (targetPane) {
          targetPane.classList.add("active");
          targetPane.removeAttribute("hidden");
        }
        syncHeights();
      });
    });
  };

  const initRatingClick = () => {
    const ratingElement = document.querySelector(".product-rating");
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");
    const tabWrapper = document.querySelector(".nav-tabs.nav-product-detail");

    if (!ratingElement || tabButtons.length < 2 || !tabWrapper) return;

    ratingElement.addEventListener("click", () => {
      tabWrapper.scrollIntoView({ behavior: "smooth", block: "start" });

      tabButtons.forEach((b) => b.classList.remove("active"));
      tabPanes.forEach((p) => p.classList.remove("active"));

      tabButtons[1].classList.add("active");
      const reviewPane = document.querySelector(
        '.tab-pane[data-tab="reviews"]'
      );
      reviewPane?.classList.add("active");

      syncHeights();
    });
  };

  return {
    init: function () {
      initOffcanvasSupport();
      initTabSwitcher();
      changePruductColor();
      initCommentFileUploadPreview();
      initStickyHeightSync();
      initRatingClick();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  product_detail.init();
});