package frontend

import (
	"goweb/frontend/composers"

	"github.com/gofiber/fiber/v2"
	"github.com/networld-solution/sctx"
)

func SetupRoutes(app *fiber.App, serviceCtx sctx.ServiceContext) {
	homeCom := composers.ComposerHomeService(serviceCtx)
	productDetailCom := composers.ComposerProductDetailService(serviceCtx)
	cartCom := composers.ComposerCartServive(serviceCtx)
	productCom := composers.ComposerProductServive(serviceCtx)
	dealsCom := composers.ComposerDealsService(serviceCtx)
	privacyCom := composers.ComposerPrivacyService(serviceCtx)

	checkoutCom := composers.ComposerCheckoutService(serviceCtx)
	foryouCom := composers.ComposerForYouService(serviceCtx)
	trendsCom := composers.ComposerTrendsService(serviceCtx)

	app.Get("/", homeCom.HomeHdl())
	app.Get("/:slug.html", productDetailCom.ProductDetailHdl())
	app.Get("/gio-hang", cartCom.CartHdl())
	app.Get("/san-pham", productCom.ProductsHdl())
	app.Get("/thanh-toan", checkoutCom.CheckoutHdl())
	app.Get("/danh-cho-ban", foryouCom.ForYouHdl())
	app.Get("/xu-huong", trendsCom.TrendsHdl())
	app.Get("/uu-dai", dealsCom.DealsHdl())
	app.Get("/chinh-sach-bao-mat", privacyCom.PrivacyHdl())
}
