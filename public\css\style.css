:root {
    --bg-color-body: #ffffff;
    --primary: #CC0D39;
    --primary-hover: #ad0b30;
    --primary-dark: #ad0b30;
    --secondary: #000;
    --secondary-hover: #000;
    --secondary-light: #A7BFFE;
    --title: #000;
    --color-body: #6b7385;
    --light-grey :#999;
    --dark-grey :#333333;
    --bg-light: #FFFAF3;
    --light-dark: rgb(254, 235, 157);
    --theme-text-color: #000000;
    --dark: #151F37;
    --light: #FFFAF3;
    --dark-light: #F9F9F9;
    --rgb-black: 0, 0, 0;
    --rgb-white: 255, 255, 255;
    --rgb-orange: 255, 153,0;
    --rgb-blue: 51, 141, 188;
    --rgb-dusty-rose: 221, 186, 191;
    --bs-light-rgb: 255,250,243;
    --rgba-primary-1: rgba(204, 13, 57, 0.1);
    --rgba-primary-2: rgba(204, 13, 57, 0.2);
    --rgba-primary-3: rgba(204, 13, 57, 0.3);
    --rgba-primary-4: rgba(204, 13, 57, 0.4);
    --rgba-primary-5: rgba(204, 13, 57, 0.5);
    --rgba-primary-6: rgba(204, 13, 57, 0.6);
    --rgba-primary-7: rgba(204, 13, 57, 0.7);
    --rgba-primary-8: rgba(204, 13, 57, 0.8);
    --rgba-primary-9: rgba(204, 13, 57, 0.9);
    --gradient1: linear-gradient(307deg, #FEEB9D 1.9%, #FFDFE7 67.57%);
    --gradient2: linear-gradient(307deg, #FFEA96 1.9%, #FFB0B0 85.96%);
    --transition: 0.3s;
    --transition-8: 0.8s;
    --transition-4: all .25s cubic-bezier(.645,.045,.355,1);  
    --shadow-1: 0px 6px 34px rgba(215, 216, 222, 0.41);
    --10px: 0.652vw;
}

body{
    background-color: rgba(var(--bs-light-rgb), 1);
    scroll-behavior: auto;
    position: relative;
    overflow-y: scroll;
    scrollbar-width: thin;
}
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(0);
    opacity: 0;
    z-index: -1;
    transition: all 0.5s ease;
    pointer-events: none;
}
body.modal-open {
  overflow: hidden;
}
body.offcanvas-open {
  overflow: hidden;
}
.no-scroll {
  overflow: hidden;
}

img {
    max-width: 100%;
    height: auto;
    transform: scale(1);
    vertical-align: middle;
    -ms-interpolation-mode: bicubic;
}
/**
 * default class
 */
.position-static{
    position: static!important;
}
.tx__hover--red:hover{
    color: var(--red);
}
.tx-center{
    text-align: center;
}
.hover-white:hover{
    background-color: rgba(var(--white-rgb), 0.3);
}
.hover-yelow:hover{
    background-color: rgba(var(--yelow-rgb), 0.3);
}
.tx-hover{
    transition: var(--transition);
}
.tx-hover:hover, .tx-hover.active{
    color: var(--red);
}
.tx-hover-line{
    position: relative;
}

.border-bottom{
    border-bottom: 1px solid var(--color-border);
}

.pb-1{
    padding-bottom: 1em;
}
.p-1{
    padding: 1em;
}
.border-radius{
    border-radius: var(--radius);
}

.tx-hover-line::after {
    position: absolute;
    content: "";
    left: auto;
    bottom: 0;
    background: currentColor;
    width: 0;
    height: 1px;
    transition: var(--transition);
    right: 0;
}
.tx-hover-line:hover::after{
    width: 100%;
    left: 0;
    right: auto;
}
.tx-hover-line.active{
    border-bottom: 1px solid var(--red);
}
.border--color{
    border: 1px solid var(--color-border);
}
.line_hf{
    position: relative;
}
.line_hf::before{
    content: "";
    background-color: rgba(var(--white-rgb),0.3);
    height: 20px;
    width: 1px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}
.readmore__btn{
    font-size: 1rem;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}
.readmore__btn__link{
    color: rgba(var(--black-rgb), 0.7);;
    position: relative;
    transition: var(--transition);
    font-size: 1.3em;
    font-weight: 500;
    display: inline-block;
}
.readmore__btn__link i{
    position: relative;
    top:1px;
    padding-left: 4px;
}
.readmore__btn__link:hover{
    color: rgba(var(--red-rgb), 0.7);
}

.readmore__btn__link::after {
    position: absolute;
    content: "";
    left: auto;
    bottom: 0;
    background: currentColor;
    width: 0;
    height: 2px;
    transition: var(--transition);
    right: 0;
}
.readmore__btn__link:hover::after{
    width: 100%;
    left: 0;
    right: auto;
}

.btn-primary {
  font-size: var(--10px);
  padding: 0.7em 1.5em;
  display: inline-flex;
  font-weight: 500;
  position: relative;
  align-items: center;
  overflow: hidden;
  z-index: 1;
  text-align: center;
  justify-content: center;
  border-radius: 2em;
  background-color: var(--light-dark);
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.btn-primary>span{
    font-size: 1.4em;
}

.btn-primary::after {
  content: "";
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  z-index: 0;
  transition: all 0.5s ease;
}

.btn-primary:hover::after {
  left: 120%;
}

.btn-primary:hover {
  color: #111 !important;
  background-color: #ffc107;
}

.btn-secondary {
    font-size: var(--10px);
    padding: 0.7em 1.5em;
    line-height: var(--vw-24);
    display: inline-flex;
    font-weight: 500;
    position: relative;
    align-items: center;
    overflow: hidden;
    z-index: 1;
    text-align: center;
    transition: all .2s;
    justify-content: center;
    border: 0.1em solid black;
    border-radius: 2em;
    background-color: var(--black);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: black;
    color: #fff;
}
.btn-secondary:hover:after {
    opacity: 0;
    transform: translate(0);
}

.btn-secondary:after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all .5s;
    opacity: .8;
    transform: translate(-105%);
    border-right: var(--vw-2) solid rgb(255, 255, 255);
    background-color: rgba(255, 255, 255, .5);
}

.btn--hover{
    border-radius: 50%;
    background-color: transparent;
    border: none;
    position: relative;
    cursor: pointer;
}
.btn--hover::before {
    background-color: rgba(var(--black-rgb), 0.2);
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    opacity: 0;
    transform: scale(0.8);
    border-radius: 100%;
    transition: var(--transition);
    z-index: -1;
}
.btn--hover:hover::before{
    opacity: 1;
    transform: scale(1);
}

.maybi-hover{
    position: relative;
    overflow: hidden;
}
.maybi-hover::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.8;
    transform: translate(-105%);
    border-right: 0.2em solid var(--white);
    background-color: rgba(255, 255, 255, .5);
    transition: all 0.5s ease;
}

.maybi-hover:hover::after{
    transform: translate(0);
    opacity: 0;
}

.bg-light {
    background-color: rgba(var(--bs-light-rgb), 1) !important;
}


@keyframes headerSticky {
    0% {
        transform: translateY(-100%);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes stickySlideDown {
    0% {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
}

.bg-white{
    background-color: var(--white);
}

.bg-cream{
    background-color: var(--cream);
}
.bg-light-cream{
    background-color: var(--light-cream);
}

.container90{
    width: 90%;
    margin: 0 auto;
}

.sec{
    font-size: var(--10px);
    padding: 3em 0;
}

.text-muted {
  color: rgb(102, 102, 102);  
  font-weight: normal;
  user-select: none;
  pointer-events: none;
}

.text-center{
    text-align: center;
}

.hidden {
  display: none !important;
}

.inline-block { 
    display: inline-block; 
}
/* --- Header top */

/* ---- product list ---- */
.product-list{
    font-size: var(--10px);
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5em;;
}

.product{
    font-size: var(--10px);
    min-width: 0;
}

.product--p10{
    padding: 1em;
}

.product__wrapper{
    aspect-ratio: 3/4;    
    position: relative;
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    overflow: hidden;
    z-index: 20;
}

.product__wrapper:hover .product__img{
    opacity: 0;
}
.product__wrapper:hover .product__img--hover{
    display: block;
    z-index: 1;
    opacity: 1;
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
}
.product__thumb{
    width: 100%;
    height: 100%;
}

.product__thumb >img{
    display: block;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition-duration: 700ms;
}

.product__img--hover{
    position: absolute;
    inset: 0;
    opacity: 0;
}

.product-btn{
    position: absolute;
    top: 1em;
    right: 0.5em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1.5em;
    z-index: 6;
}

.product-icon{
    width: 4.2em;
    height: 4.2em;
    border-radius: 50%;
    background-color: var(--white);   
    display: flex;
    align-items: center;
    justify-content: center; 
    opacity: 0;
    visibility: hidden;
    transform: translate(2em);
    transition: all 0.3s ease;
    box-shadow: 0 1.5em 1.5em 0 rgba(0,0,0,.05);
    position: relative;
}

.product-icon__heart,.product-icon__cart{
    width: 2.2em;
    height: 2.2em;    
    cursor: pointer;
}

.product__wrapper:hover .product-icon{
    opacity: 1;
    visibility: visible;
    transform: translate(0);
}

.product__wrapper:hover .product-size{
    opacity: 1;
    transform: translateX(.5em);
}

.product-icon:hover{
    background: rgba(var(--rgb-black),0.8);
    fill: var(--white);
}

.product__wrapper:hover .product-view{
    opacity: 1;
    visibility: visible;
    bottom: 0.3em;
}

.product-view{
    font-size: var(--10px);
    width: 13em;
    height: 4em;
    position: absolute;
    left: 50%;
    bottom: -1em;
    transform: translateX(-50%);
    background: rgba(var(--rgb-black),0.8);
    color: var(--white);
    border-radius: 2em;
    z-index: 30;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}
.product-view::before{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    border: 0.2em solid var(--white);
    background: transparent;
    transition: all 0.3s ease;
    border-radius: 2em;   
}
.product-view:hover{
    background-color: var(--primary);
}
.product-view>span{
    font-size: 1.4em;
    text-transform: uppercase;
}
.product-tag{
    font-size: var(--10px);
    font-weight: 600;
    position: absolute;
    top: 1em;
    left: 0.5em;    
    border-radius: 1.5em;
    z-index: 30; 
    box-shadow: var(--shadow-1);
    display: flex;
    flex-direction: column;
    gap: 0.6em;
}
.product-tag__item {
    border-radius: 1.5em;
}
.product-tag__item--sale{
    background-color: var(--primary);
    color: var(--white);  
}
.product-tag__item--new{
    background-color: var(--white);    
}
.product-tag__item > span {
    font-size: 1.2em;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 5em;
    height: 1.8em;
    border-radius: 1.5em;
}

.product-tag--sale{
    background-color: var(--primary);
    color: var(--white);  
}
.product-tag--new{
    background-color: var(--white);    
}
.product-tag--hot{
    background-color: var(--light-dark);    
}
.product-tag>span{
    font-size: 1.2em;
    padding: 0.2em 1em;
    display: inline-flex;
    justify-content: center;
}

.product-size{
    position: absolute;
    left: 0.2em;
    bottom: 3em;
    padding:0.2em;
    border-radius: 2em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: .6em;
    background-color: rgba(var(--rgb-white), 0.8);
    z-index: 30;
    opacity: 0;
    transform: translate(0);
    transition: all 0.3s ease;
}

.product-size__item{
    font-size: 1.3em;
    width: 3em;
    height: 3em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 500;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    background-color: rgba(var(--rgb-white), 0.3);
    transition: all 0.3s ease;
}
.product-size__item.active{
    background-color: var(--primary);
    color: var(--white);
}
.product-size__item.product-size--active {
    background-color: var(--primary);
    color: var(--white);
}
.product-size__item:hover{
    background-color: var(--black);
    color: var(--white);
}

.product__info{
    font-size: var(--10px);
    padding: 1em;   
    display: flex;
    flex-direction: column;
    gap: 1em;
    background-color: var(--white);
    border-bottom-left-radius: 1em;
    border-bottom-right-radius: 1em;
}

.product__title{    
    font-weight: 500;
    height: 3.3em;
    display: flex;
}

.product__title>a{
    font-size: 1.3em;  
    line-height: 1.21; 
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;  
    transition: all 0.3s ease;
}

.product__title>a:hover{
    color: var(--primary);
}

.product-rating{
    font-size: var(--10px);
    display: flex;
    align-items: end;
    gap:1em;
}

.mb-rating{
    display: flex;
    align-items: center;
}
.mb-start-fill,.mb-start{
    width: 1.4em;
    height: 1.4em;
}
.mb-start-fill>svg, .mb-start>svg{
    width: 100%;
    height: 100%;
}

.product-rating__selled{
    font-size: 1.2em;
    font-weight: 500;
    color: rgba(var(--rgb-black), 0.5);   
    line-height: 0.9;
}

.product-prices{
    font-size: var(--10px);
    line-height: 1;    
}

.product-prices>span{
    font-size: 1.8em;
    font-weight: 700;
    color: var(--primary);    
}

.product-prices>span + span{
    font-size: 1.3em;
    color: rgba(var(--rgb-black), 0.7);
    font-weight: 500;
    margin-left: 0.5em;
    text-decoration: line-through;
}

.product-colors{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    gap:0.5em;    
}

.product-color{
    width: 3.5em;
    height: 2.2em;
    border-radius: 1.5em;    
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}

.product-color.active, .product-color--active{
    box-shadow: var(--white) 0px 0px 0px 0.1em, var(--black) 0px 0px 0px 0.2em;
}

.product-color__item{
    width: 3.5em;
    height: 2.2em;;
    border-radius: 1.5em;
    transition: all 0.3s ease-in-out;
}

.product-color--more{
    width: 2.2em;
    height: 2.2em;;
    border-radius: 50%;
    background-color: rgba(var(--rgb-black),0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.product-color--more>span{
    font-size: 1.2em;
    font-weight: 500;
}

.sec-head{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2em;
}

.sec-head__title{
    font-size: 3.2em;
    font-weight: 700;
}

.sec-head__list{
    display: flex;
    align-items: center;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    border-radius: 2em;
    gap: 2em;
    padding: 0.5em;
}

.sec-head__item{
    font-size: 1.4em;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 6em;
    height: 2.5em; 
    border-radius: .5em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sec-head__item:hover{
    background-color: var(--primary);
    color: var(--white);
}

/* vouchers home */
.vouchers{
    font-size: var(--10px);
    padding-top: 6em;
}
.voucher__wrapper{
    font-size: var(--10px);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2em;
    align-items: center;
    justify-content: center;
}

/* fresship progress */
.freeship_progress{
    font-size: var(--10px);
    background-color: rgb(245, 246, 236);
    border-radius: 1.2em;
    padding: 2.3em 1.6em 1.6em 1.6em;
    height: 9em;
    position: relative;
}

.freeship_progress__bar{
    margin: 1.2em 0 1.6em 0;
    width: 100%;
    background-color: var(--white);
    height: 0.8em;
    position: relative;
}

.freeship_progress__bar-value{
    height: 100%;
    background: linear-gradient(90deg, #19450f 0%, #3dab25 100%);
    position: relative;
    transition: width 2s ease;
}

.freeship-icon__progress {
  position: absolute;
  left: 95%;
  top: 50%;
  transform: translateY(-50%);
  width: 3.2em;
  height: 3.2em;
  border: 0.2em solid #3dab25;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white);
  border-radius: 50%;
  position: relative;
}

.freeship-icon__amount {
  position: absolute;
  bottom: 105%;
  left: 50%;
  transform: translateX(-50%) translateY(-0.4em);
  background-color: #fff;
  padding: 0.2em 0.6em;
  font-size: 0.75em;
  border-radius: 0.6em;
  color: #19450f;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 0 0.4em rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.freeship__icon{
    width: 2em;
    height: 2em;
}

.freeship_progress__bar-end {
  position: absolute;
  right: 0;
  top: 270%;
  color: #444;
  font-weight: 800;
}

.freeship-message-success {
  color: rgb(39, 108, 21);
  font-weight: 600;
  animation: pulse 1s ease infinite;
  display: inline-block;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.04);
  }
}

/** Breadcrumb image**/
.page-banner {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 0.5em;
  width: 100%;
  aspect-ratio: 1920 / 300;
  overflow: hidden;
}


.page-banner__img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
  z-index: -1;
}

.page-banner__title {
    font-size: 4em;
    color: var(--white);
    font-weight: 600;
    position: relative;
    text-transform: uppercase;
}
.page-banner:before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
}

.page-banner__breadcrumb-list {
    display: flex;
    color: var(--white);
    position: relative;
    gap: 0.5em;
    font-size: 1.6em;
    
}

.voucher-disabled {
  opacity: 0.6;
  pointer-events: auto;
  cursor: not-allowed;
}

.cart-vouchers__selected{
    font-size: var(--10px);
    display: flex;
    gap: 0.8em;
    flex-wrap: wrap;
}

.cart-vouchers__item{
    position: relative;
    overflow: hidden;
    z-index: 0;
    display: inline-flex;
    margin-right: 0.5em;
}

.cart-vouchers__chip {
  align-items: center;
  padding: 0.6em 1em;
  border-radius: 0.4em;
  border: 0.2em solid;
}

.cart-vouchers__item::before,
.cart-vouchers__item::after {
  content: "";
  position: absolute;
  width: 1.2em;
  height: 1.2em;
  background-color: var(--light);
  border: 0.2em solid;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.cart-vouchers__item::before {
  left: -0.6em;
}

.cart-vouchers__item::after {
  right: -0.6em;
}

.cart-vouchers__chip.discount {
  background-color: #e6f3ff;
  color: #007bff;
  border-color: #007bff;
}

.cart-vouchers__chip.freeship {
  background-color: #fff3e0;
  color: #ff6f00;
  border-color: #ff6f00;
}

.cart-vouchers__item.freeship::before,
.cart-vouchers__item.freeship::after {
  border-color: #ff6f00;
}

.cart-vouchers__item.discount::before,
.cart-vouchers__item.discount::after {
  border-color: #007bff;
}

.cart-vouchers__chip > span{
  font-size: 1.4em;
  font-weight: bold;
  line-height: 1;
}

/* ---- footer - mobile ---- */
.m_extra_nav{
    display: flex;
    position: fixed;
    left: 0;
    bottom: -11em;
    width: 100%;
    background-color: rgba(var(--rgb-white), 0.95);   
    align-items: center;
    justify-content: space-between;
    font-size: var(--10px);
    height: 10em;
    padding-bottom: 2em;
    z-index: 20;
    transition: all .5s ease .5s;
}

.m_extra_nav__item{
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.m_extra_nav__link{
    display: block;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;    
    position: relative;
    gap: 0.5em;
}
.m_extra_nav__icon{
    width: 4em;
    height: 4em;
    fill: var(--black);
    stroke-width: 0.5em;
    transition: all 0.3s ease;
}
.m_extra_nav__text{
    font-size: 1.3em;
    color: var(--black);
    font-weight: 500;
}

.m_extra_nav__count{
    font-size: 1.2em;
    position: absolute;
    top: 0;
    right: 0.7em;
    width: 2em;
    height: 2em;
    border-radius: 50%;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}