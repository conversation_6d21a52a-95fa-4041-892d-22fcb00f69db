package cart

import (
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
    "goweb/views/partials"
	"goweb/views/cart/components"
)

templ Cart() {
@layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) { 
    <section class="cart" id="cart">
        @partials.BreadcrumbImage()
        <div class="container90">
            <div class="cart__wrapper">
                <div class="cart__content hidden">
                    <div class="cart__notification-sold">
                        <img class="icon-fire" src={templates.AssetURL("/static/images/icon-fire.png")} alt="img" width="48" height="49">
                        <div class="notification-sold__count">
                            Giỏ hàng sẽ giữ trong
                            <span class="notification-sold__time" id="cart-timer" data-minutes="10"> 00:00</span>
                            phút. Thanh toán sớm để không lỡ món yêu thích nhé!
                        </div>
                    </div>
                    <div class="products-cart">
                        <div class="cart-header">
                            <div class="cart-header__title">Sản phẩm</div>
                            <div class="cart-header__title text-center">Giá</div>
                            <div class="cart-header__title text-center">Số lượng</div>
                            <div class="cart-header__title text-center">Tổng cộng</div>
                            <div></div>
                        </div>
                    </div>
                </div>
                <div class="cart__summary hidden">
                    <h4 class="cart__summary__title">Tổng đơn hàng</h4>
                    <div class="cart-summary__box">
                        @partials.FreeshipProgress()
                        <button class="cart-vouchers__button btn-secondary btn-open-vouchers">Chọn Voucher</button>
                        <div class="cart-vouchers__show">
                            <svg class="cart-vouchers__icon">
                                <use href="#icon-voucher"></use>
                            </svg>
                            <span>Bạn có <strong>3</strong> voucher</span>
                        </div>
                        <div class="cart-vouchers__selected">
                            <div class="cart-vouchers__item freeship">
                                <span class="cart-vouchers__chip freeship">
                                    <span>Giảm 18%</span>
                                </span>
                            </div>
                            <div class="cart-vouchers__item discount">
                                <span class="cart-vouchers__chip discount">
                                    <span>Giảm 10%</span>
                                </span>
                            </div>
                        </div>

                        <div class="cart-summary__save" style="display:none">
                            <svg class="cart-save__icon">
                                <use href="#icon-checked"></use>
                            </svg>
                            <span>Bạn tiết kiệm được <strong>50.000đ</strong> cho đơn hàng này</span>
                        </div>
                        <div class="cart-summary__total">
                            <div class="cart-summary__total-row">
                                <span class="text-muted">Tạm tính</span>
                                <strong data-type="subtotal">0</strong>
                            </div>
                            <div class="cart-summary__total-row">
                                <span class="text-muted">Phí vận chuyển</span>
                                <strong data-type="shipping">0</strong>
                            </div>
                            <div class="cart-summary__total-row">
                                <span class="text-muted">Mã giảm giá</span>
                                <strong  data-type="discount">0</strong>
                            </div>
                            <div class="cart-summary__total-row">
                                <span>Tổng cộng</span>
                                <strong class="cart__total__price" data-type="total">0</strong>
                            </div>
                        </div>
                        <a href="/thanh-toan" class="cart-order__button btn-secondary">Thanh toán</a>
                    </div>
                </div>
                <div class="cart__empty hidden">
                    <img src={templates.AssetURL("/static/images/empty-cart.png")} alt="Giỏ hàng của bạn đang trống" loading="lazy"></img>
                    <h3>Chưa có sản phẩm trong giỏ hàng</h3>
                    <p class="text-muted">Về trang cửa hàng để chọn mua sản phẩm bạn nhé!!</p>
                    <a href={templates.AssetURL("")} class="btn btn-primary" title="Trang chủ">Mua sắm ngay</a>
                </div>
            </div>
        </div>
    </section>
    @components.OffcanvasVouchers()
}    
}

templ head(){
	<link rel="stylesheet" href={templates.AssetURL("/static/css/cart.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/offcanvas_vouchers.css")}>
}

templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/js/cart.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/js/vouchers_process.js")}></script>
}