package privacy

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/privacy/components"
	"goweb/views/layouts"
	"goweb/views/partials"
)

templ Privacy() {
	@layouts.Master(nil, &[]templ.Component{headPrivacy()}, scriptPrivacy()) {
		@components.PrivacyBackground()
		@components.PrivacyContent()
		@partials.OffcanvasCart()
	}
}

templ headPrivacy() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/privacy.css") }/>
}

templ scriptPrivacy() {
	<script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/privacy.js")}></script>
}
