package layouts

import (
	"github.com/networld-solution/gos/templates"
    "goweb/views/partials"
    "goweb/frontend/entity"
)

templ Master(seo *entity.Seo, head *[]templ.Component, footers ...templ.Component) {
    <!doctype html>
    <html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="sm-hover" data-sidebar-image="none" data-preloader="disable">
        <head>
            <meta charset="UTF-8">
            <title>MAYBI Womenswear - Thương hiệu thời trang nữ thiết kế</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="description" content="Maybi - thương hiệu dành cho những người phụ nữ thanh lịch, tinh tế và hiện đại. Maybi giú<PERSON> mọi phụ nữ tỏa sáng rực rỡ nhất, tiên phong với những thiết kế tôn lên mọi vóc dáng, k<PERSON> lê<PERSON> đ<PERSON> cong, che mọ<PERSON> khu<PERSON>ết điểm">
            <link rel="shortcut icon" href={templates.AssetURL("/static/images/fav.png")} type="image/x-icon">
            <link rel="stylesheet" href={templates.AssetURL("/static/css/default.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/icons.min.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/maybi-ui.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/style.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/menus.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/menus-rep.css")}>
            <link rel="stylesheet" href={templates.AssetURL("/static/css/footer.css")}>  
            <link rel="stylesheet" href={templates.AssetURL("/static/css/offcanvas_cart.css")}>

            if head != nil && len(*head) > 0 {
                for _, item := range *head{
                    @item
                }
            }
            <link rel="stylesheet" href={templates.AssetURL("/static/css/reponsive.css")}>
        </head>
        <body class="maybi-header-sticky">
            @Header()
            { children... }
            @Footer()
            @partials.OffcanvasCart()
            @partials.ExtraNavMobile()
            @partials.SvgSymbols() 
            <script type="text/javascript" src={templates.AssetURL("/static/js/app.js")}></script>
            <script src={templates.AssetURL("/static/js/cart_updater.js")}></script>
            if len(footers) > 0 {
                for _, footer := range footers {
                    @footer
                }
            }
        </body>
    </html>
}