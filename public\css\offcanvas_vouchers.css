/** Offcanvas vouchers */

.voucher__section {
  margin: 2em auto;
}

.voucher__section-title {
  font-size: 1.6em;
  font-weight: 600;
  margin-bottom: 1em;
  padding-left: 0.5em;
  border-left: 0.3em solid var(--primary-color, #f60);
  color: #222;
}

.with-divider {
  border-top: 0.1em solid rgba(0, 0, 0, 0.2);
  padding-top: 2em;
  margin-top: 2em;
  font-size: var(--10px);
}

.voucher__list {
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.voucher-item-wrapper{
    display: flex;
    align-items: center;
    width: 100%;
    gap: 1em;
}

.voucher-item-wrapper__radio > input{
    font-size: var(--10px);
    width: 2em;
    height: 2em;
    border: 0.1em solid var(--black);
}
.voucher-item{
    width: 100%;
}

@media (max-width: 768px){  
    .voucher__section-title{
        font-size: 2.6vw;
    }

    .cart-vouchers__button, .cart-vouchers__show {
        padding: 1.3em 1.875em;
    }

    .cart-vouchers__show{
        justify-content: center;
    }
}