package components

import (
    "github.com/networld-solution/gos/templates"
)

templ ProductCartItem() {
<div class="cart__item product__item product">
    <div class="col__information">
        <div class="col__information__img">
            <img class="product__img" src={templates.AssetURL("/static/images/blazer-green.jpg")}
                alt="Áo blazer form rộng" />
        </div>
        <div class="col__information__content product__info">
            <strong class="cart__item__title">
                <a href="#" title="Áo blazer form rộng">Áo blazer form rộng</a>
            </strong>
            <div class="cart__item__variants">
                <span class="text-muted">Size:</span>
                <ul class="product-size">
                    <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}]'
                        class="product-size__item maybi-hover">S
                    </li>
                    <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}]'
                        class="product-size__item maybi-hover">M
                    </li>
                    <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}, {"color": "#eb6609", "img": "/static/images/blazer-cam.jpg"}, {"color": "#ecdac1", "img": "/static/images/blazer-kem.jpg"}, {"color": "#ecdac1", "img": "/static/images/blazer-kem.jpg"}]'
                        class="product-size__item maybi-hover product-size--active">L
                    </li>
                </ul>

            </div>
            <div class="cart__item__variants">
                <span class="text-muted">Màu:</span>
                <ul class="product-colors">
                    <li class="product-color active"
                        data-img={templates.AssetURL("/static/images/blazer-den.jpg")}>
                        <span class="product-color__item" style="background-color: black;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-xanh-reu.jpg")}>
                        <span class="product-color__item" style="background-color: #607c2c;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-green.jpg")}>
                        <span class="product-color__item" style="background-color: #225c48;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-blue.jpg")}>
                        <span class="product-color__item" style="background-color: #c9e6ff;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-pink.jpg")}>
                        <span class="product-color__item" style="background-color: #f32250;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                        <span class="product-color__item" style="background-color: #eb6609;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                        <span class="product-color__item" style="background-color: #6e7ec3;"></span>
                    </li>
                    <li class="product-color"
                        data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                        <span class="product-color__item" style="background-color: #f7b789;"></span>
                    </li>
                </ul>
            </div>

        </div>
    </div>
    <strong class="col__price text-muted cart__item__price--sale text-center" data-label="Price">50.000đ</strong>
    <div class="quantity-cart">
        <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
            <svg class="product-icon__down">
                <use href="#icon-down"></use>
            </svg>
        </button>
        <input class="quantity-cart__input" type="text" aria-label="Số lượng sản phẩm" value="1" />
        <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
            <svg class="product-icon__up">
                <use href="#icon-up"></use>
            </svg>
        </button>
    </div>
    <div class="col__total product-total text-center" data-label="Subtotal">1.299.000đ</div>
    <div class="col__delete cart__item__delete">
        <svg class="col-icon__delete">
            <use href="#icon-close"></use>
        </svg>
    </div>
</div>
}